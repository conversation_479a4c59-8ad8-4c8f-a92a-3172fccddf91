#!/usr/bin/env python3
"""
Script để xóa slide trong Google Slides presentation dựa trên slideID
"""

import asyncio
import sys
import os
import logging

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.google_slides_service import GoogleSlidesService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def delete_slide_by_id(presentation_id: str, slide_id: str):
    """
    Xóa một slide dựa trên slideID
    
    Args:
        presentation_id: ID của presentation
        slide_id: ID của slide cần xóa
    """
    
    print(f"🗑️ Deleting slide from presentation...")
    print(f"   Presentation ID: {presentation_id}")
    print(f"   Slide ID to delete: {slide_id}")
    
    slides_service = GoogleSlidesService()
    
    if not slides_service.is_available():
        print("❌ Google Slides service not available")
        return None
    
    try:
        print(f"\n🔍 Step 1: Checking presentation and slide...")
        
        # Kiểm tra presentation và slide
        presentation = slides_service.slides_service.presentations().get(
            presentationId=presentation_id
        ).execute()
        
        current_slides = presentation.get('slides', [])
        current_slide_ids = [slide.get('objectId') for slide in current_slides]
        
        print(f"   Current slides ({len(current_slides)} total): {current_slide_ids}")
        
        if slide_id not in current_slide_ids:
            print(f"❌ Slide {slide_id} not found in presentation")
            return {
                "success": False,
                "error": f"Slide {slide_id} not found",
                "current_slides": current_slide_ids
            }
        
        # Kiểm tra nếu chỉ còn 1 slide (Google Slides cần ít nhất 1 slide)
        if len(current_slides) <= 1:
            print(f"❌ Cannot delete slide - presentation must have at least 1 slide")
            return {
                "success": False,
                "error": "Cannot delete last slide - presentation must have at least 1 slide",
                "current_slides": current_slide_ids
            }
        
        print(f"✅ Slide found and can be deleted")
        print(f"   Slides remaining after deletion: {len(current_slides) - 1}")
        
        print(f"\n🗑️ Step 2: Deleting slide...")
        
        # Tạo request xóa slide
        requests = [{
            'deleteObject': {
                'objectId': slide_id
            }
        }]
        
        # Thực hiện xóa
        result = slides_service.slides_service.presentations().batchUpdate(
            presentationId=presentation_id,
            body={'requests': requests}
        ).execute()
        
        print(f"✅ Slide deleted successfully!")
        print(f"   API result: {result}")
        
        # Kiểm tra kết quả
        updated_presentation = slides_service.slides_service.presentations().get(
            presentationId=presentation_id
        ).execute()
        
        updated_slides = updated_presentation.get('slides', [])
        updated_slide_ids = [slide.get('objectId') for slide in updated_slides]
        
        print(f"\n📊 Updated presentation:")
        print(f"   Total slides: {len(updated_slides)}")
        print(f"   Remaining slide IDs: {updated_slide_ids}")
        
        if slide_id not in updated_slide_ids:
            print(f"✅ Deletion verified - slide {slide_id} removed")
        else:
            print(f"❌ Deletion failed - slide {slide_id} still exists")
        
        return {
            "success": True,
            "deleted_slide_id": slide_id,
            "presentation_id": presentation_id,
            "remaining_slides": len(updated_slides),
            "remaining_slide_ids": updated_slide_ids
        }
        
    except Exception as e:
        print(f"❌ Error deleting slide: {e}")
        logger.exception("Delete slide error:")
        return {
            "success": False,
            "error": str(e),
            "slide_id": slide_id
        }

async def delete_multiple_slides(presentation_id: str, slide_ids: list, safe_mode: bool = True):
    """
    Xóa nhiều slides cùng lúc
    
    Args:
        presentation_id: ID của presentation
        slide_ids: List các slide IDs cần xóa
        safe_mode: Nếu True, xóa từng slide một; nếu False, xóa batch
    """
    
    print(f"🗑️ Deleting multiple slides...")
    print(f"   Presentation ID: {presentation_id}")
    print(f"   Slide IDs to delete: {slide_ids}")
    print(f"   Safe mode: {safe_mode}")
    
    slides_service = GoogleSlidesService()
    
    if not slides_service.is_available():
        print("❌ Google Slides service not available")
        return None
    
    try:
        print(f"\n🔍 Step 1: Validating slides...")
        
        # Kiểm tra presentation
        presentation = slides_service.slides_service.presentations().get(
            presentationId=presentation_id
        ).execute()
        
        current_slides = presentation.get('slides', [])
        current_slide_ids = [slide.get('objectId') for slide in current_slides]
        
        print(f"   Current slides ({len(current_slides)} total): {current_slide_ids}")
        
        # Validate slides tồn tại
        valid_slide_ids = []
        invalid_slide_ids = []
        
        for slide_id in slide_ids:
            if slide_id in current_slide_ids:
                valid_slide_ids.append(slide_id)
                print(f"   ✅ {slide_id} found")
            else:
                invalid_slide_ids.append(slide_id)
                print(f"   ❌ {slide_id} not found")
        
        if not valid_slide_ids:
            print("❌ No valid slides to delete")
            return {
                "success": False,
                "error": "No valid slides found",
                "invalid_slides": invalid_slide_ids
            }
        
        # Kiểm tra nếu xóa hết slides
        remaining_after_delete = len(current_slides) - len(valid_slide_ids)
        if remaining_after_delete <= 0:
            print(f"❌ Cannot delete all slides - presentation must have at least 1 slide")
            return {
                "success": False,
                "error": "Cannot delete all slides - presentation must have at least 1 slide",
                "slides_to_delete": valid_slide_ids,
                "current_slides_count": len(current_slides)
            }
        
        print(f"✅ Can delete {len(valid_slide_ids)} slides")
        print(f"   Slides remaining after deletion: {remaining_after_delete}")
        
        deleted_slides = []
        failed_slides = []
        
        if safe_mode:
            print(f"\n🗑️ Step 2: Deleting slides one by one (safe mode)...")
            
            # Xóa từng slide một (từ cuối lên đầu để tránh index issues)
            valid_slide_ids_reversed = list(reversed(valid_slide_ids))
            
            for i, slide_id in enumerate(valid_slide_ids_reversed):
                try:
                    print(f"   Deleting slide {i+1}/{len(valid_slide_ids_reversed)}: {slide_id}")
                    
                    result = slides_service.slides_service.presentations().batchUpdate(
                        presentationId=presentation_id,
                        body={'requests': [{
                            'deleteObject': {
                                'objectId': slide_id
                            }
                        }]}
                    ).execute()
                    
                    deleted_slides.append(slide_id)
                    print(f"   ✅ Successfully deleted: {slide_id}")
                    
                except Exception as e:
                    failed_slides.append({"slide_id": slide_id, "error": str(e)})
                    print(f"   ❌ Failed to delete {slide_id}: {e}")
        
        else:
            print(f"\n🗑️ Step 2: Deleting slides in batch...")
            
            # Xóa batch (riskier nhưng nhanh hơn)
            requests = []
            for slide_id in reversed(valid_slide_ids):  # Reverse order
                requests.append({
                    'deleteObject': {
                        'objectId': slide_id
                    }
                })
            
            try:
                result = slides_service.slides_service.presentations().batchUpdate(
                    presentationId=presentation_id,
                    body={'requests': requests}
                ).execute()
                
                deleted_slides = valid_slide_ids
                print(f"✅ Batch deletion completed")
                
            except Exception as e:
                failed_slides = [{"slide_id": sid, "error": str(e)} for sid in valid_slide_ids]
                print(f"❌ Batch deletion failed: {e}")
        
        # Kiểm tra kết quả
        updated_presentation = slides_service.slides_service.presentations().get(
            presentationId=presentation_id
        ).execute()
        
        updated_slides = updated_presentation.get('slides', [])
        updated_slide_ids = [slide.get('objectId') for slide in updated_slides]
        
        print(f"\n📊 Deletion summary:")
        print(f"   Successfully deleted: {len(deleted_slides)} slides")
        print(f"   Failed to delete: {len(failed_slides)} slides")
        print(f"   Invalid slides: {len(invalid_slide_ids)} slides")
        print(f"   Final slide count: {len(updated_slides)}")
        print(f"   Remaining slide IDs: {updated_slide_ids}")
        
        return {
            "success": len(failed_slides) == 0,
            "deleted_slides": deleted_slides,
            "failed_slides": failed_slides,
            "invalid_slides": invalid_slide_ids,
            "presentation_id": presentation_id,
            "remaining_slides": len(updated_slides),
            "remaining_slide_ids": updated_slide_ids
        }
        
    except Exception as e:
        print(f"❌ Error deleting multiple slides: {e}")
        logger.exception("Delete multiple slides error:")
        return {
            "success": False,
            "error": str(e),
            "slide_ids": slide_ids
        }

async def main():
    """Main function để test delete slides"""
    
    print("🧪 Google Slides Delete Script")
    print("=" * 40)
    
    # Thay đổi các giá trị này theo presentation thực tế của bạn
    PRESENTATION_ID = "1_4tAO46omZAXEAwqeTV6U0fWWBoklSI22EZ_FFf0UB0"  # Example ID
    SLIDE_ID_TO_DELETE = "slide_008_copy_of_p5"  # Thay bằng slide ID thực tế
    
    print("📋 Test 1: Delete single slide")
    result1 = await delete_slide_by_id(
        presentation_id=PRESENTATION_ID,
        slide_id=SLIDE_ID_TO_DELETE
    )
    
    if result1:
        print(f"✅ Single delete result: {result1}")
    
    print("\n📋 Test 2: Delete multiple slides")
    slide_ids_to_delete = [
        "slide_id_1",  # Thay bằng slide IDs thực tế
        "slide_id_2",
        "slide_id_3"
    ]
    
    result2 = await delete_multiple_slides(
        presentation_id=PRESENTATION_ID,
        slide_ids=slide_ids_to_delete,
        safe_mode=True  # True = xóa từng slide một, False = xóa batch
    )
    
    if result2:
        print(f"✅ Multiple delete result: {result2}")
    
    print(f"\n🔗 Check result: https://docs.google.com/presentation/d/{PRESENTATION_ID}/edit")

if __name__ == "__main__":
    asyncio.run(main())
