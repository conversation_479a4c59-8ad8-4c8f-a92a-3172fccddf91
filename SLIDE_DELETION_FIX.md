# Slide Deletion Fix - Cải tiến xóa template slides

## 🔍 Vấn đề gốc

Hệ thống không thể xóa được template slides sau khi tạo content slides, có thể do:

1. **Template slide IDs thay đổi** sau khi tạo slides mới
2. **Thứ tự xóa** gây conflict
3. **Batch delete** gây lỗi khi có slide không tồn tại
4. **Google Slides constraints** (cần ít nhất 1 slide)

## 🔧 Các cải tiến đã thực hiện

### 1. **Lưu Template IDs ngay sau copy**

**Trước:**
```python
# Lưu template IDs từ slides_content (có thể đã thay đổi)
original_template_slide_ids = slides_content.get("original_template_slide_ids", [])
```

**Sau:**
```python
# Lưu template IDs ngay sau khi copy template (trướ<PERSON> khi tạo slides mới)
original_template_slide_ids = [slide.get("slideId") for slide in copy_and_analyze_result.get("slides", [])]
logger.info(f"📋 Saved original template slide IDs immediately after copy: {original_template_slide_ids}")
```

### 2. **Cải tiến logic xóa slides**

**Trước:** Batch delete tất cả slides cùng lúc
**Sau:** Xóa từng slide một để dễ debug và xử lý lỗi

```python
# Xóa từng slide một để tránh conflicts
deleted_slides = []
failed_slides = []

for i, slide_id in enumerate(slides_to_delete_reversed):
    try:
        result = self.slides_service.presentations().batchUpdate(
            presentationId=presentation_id,
            body={'requests': [{
                'deleteObject': {
                    'objectId': slide_id
                }
            }]}
        ).execute()
        
        deleted_slides.append(slide_id)
        logger.info(f"✅ Successfully deleted slide: {slide_id}")
        
    except HttpError as e:
        failed_slides.append({"slide_id": slide_id, "error": str(e)})
        logger.error(f"❌ Failed to delete slide {slide_id}: {e}")
```

### 3. **Thêm validation và safety checks**

```python
# Kiểm tra nếu xóa hết slides (Google Slides cần ít nhất 1 slide)
remaining_slides = len(current_slides) - len(slides_to_delete)
if remaining_slides <= 0:
    logger.error("❌ Cannot delete all slides - Google Slides requires at least 1 slide")
    return {
        "success": False,
        "error": "Cannot delete all slides - presentation must have at least 1 slide"
    }
```

### 4. **Thêm debug methods**

```python
async def debug_presentation_state(self, presentation_id: str, description: str = ""):
    """Debug method để kiểm tra trạng thái presentation"""
    
    presentation = self.slides_service.presentations().get(
        presentationId=presentation_id
    ).execute()

    slides = presentation.get('slides', [])
    slide_ids = [slide.get('objectId') for slide in slides]
    
    logger.info(f"📊 Presentation info:")
    logger.info(f"   Title: {presentation.get('title', 'Untitled')}")
    logger.info(f"   Total slides: {len(slides)}")
    logger.info(f"   Slide IDs: {slide_ids}")
```

### 5. **Cải tiến logging và error handling**

```python
# Debug trạng thái trước và sau khi xóa
await self.slides_service.debug_presentation_state(
    presentation_id,
    "Before template cleanup"
)

# Xóa slides với detailed logging
delete_result = await self.slides_service.delete_all_template_slides(
    presentation_id,
    original_template_slide_ids
)

# Log chi tiết kết quả
if delete_result.get("success"):
    deleted_count = delete_result.get("slides_deleted", 0)
    failed_slides = delete_result.get("failed_slides", [])
    
    logger.info(f"✅ Template cleanup completed:")
    logger.info(f"   - Slides deleted: {deleted_count}")
    logger.info(f"   - Failed slides: {len(failed_slides)}")
    
    if failed_slides:
        for failed in failed_slides:
            logger.warning(f"   ⚠️ Failed to delete {failed['slide_id']}: {failed['error']}")
```

## 🧪 Test Scripts để debug

### 1. **test_deletion_simple.py**
- Test manual copy template và delete
- Debug từng bước với detailed logging
- Kiểm tra trạng thái presentation trước/sau xóa

### 2. **test_slide_deletion.py**
- Test toàn bộ luồng copy và delete
- Simulate tạo slides mới rồi xóa template
- Verify kết quả cuối cùng

## 📊 Cải tiến Return Value

**Trước:**
```python
return {
    "success": True,
    "slides_deleted": len(slides_to_delete),
    "deleted_slide_ids": slides_to_delete
}
```

**Sau:**
```python
return {
    "success": len(failed_slides) == 0,  # Success nếu không có slide nào fail
    "slides_deleted": len(deleted_slides),
    "deleted_slide_ids": deleted_slides,
    "failed_slides": failed_slides,
    "slides_not_found": slides_not_found,
    "remaining_slides": remaining_slides,
    "total_attempted": len(slides_to_delete)
}
```

## 🔍 Debugging Steps

Để debug vấn đề xóa slides:

1. **Chạy test script:**
   ```bash
   python test_deletion_simple.py
   ```

2. **Kiểm tra logs:**
   - Template slide IDs được lưu đúng chưa
   - Slides có tồn tại trong presentation không
   - Error messages cụ thể khi xóa fail

3. **Kiểm tra presentation:**
   - Truy cập link Google Slides để xem trực tiếp
   - Verify slides nào còn lại sau khi xóa

## 🎯 Expected Results

Sau khi fix:
- ✅ Template slides được xóa thành công
- ✅ Chỉ còn lại content slides
- ✅ Detailed logging cho debug
- ✅ Graceful error handling
- ✅ Safety checks để tránh xóa hết slides

## 🚨 Troubleshooting

Nếu vẫn không xóa được:

1. **Kiểm tra permissions:** Đảm bảo service account có quyền edit
2. **Kiểm tra slide IDs:** Verify IDs có đúng và tồn tại không
3. **Kiểm tra constraints:** Google Slides cần ít nhất 1 slide
4. **Kiểm tra API limits:** Có thể bị rate limiting

Sử dụng debug methods để trace từng bước và identify root cause.
