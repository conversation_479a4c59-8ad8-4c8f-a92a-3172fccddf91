#!/usr/bin/env python3
"""
Test script đơn giản để kiểm tra vấn đề xóa slides
"""

import asyncio
import sys
import os
import logging

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.slide_generation_service import SlideGenerationService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_simple_slide_generation():
    """Test slide generation với debug để xem vấn đề xóa slides"""
    
    print("🧪 Testing Simple Slide Generation with Deletion Debug")
    print("=" * 60)
    
    slide_service = SlideGenerationService()
    
    if not slide_service.is_available():
        print("❌ Slide service not available")
        return
    
    # Test data đơn giản
    lesson_content = """
    Bài học: Test Deletion
    
    1. Slide đầu tiên
    - Nội dung slide 1
    
    2. Slide thứ hai  
    - Nội dung slide 2
    """
    
    # Template ID (cần thay bằng ID thực tế)
    template_id = "1BdGYMghAcNplveEd4oaOdBdGYMghAcNplveEd4oaOdBdGYMghAcNplveEd4oaOd"  # Example ID
    
    try:
        print("🚀 Starting slide generation...")
        
        result = await slide_service.generate_slides_from_lesson(
            lesson_id="test_deletion_001",
            lesson_content=lesson_content,
            template_id=template_id,
            presentation_title="Test Deletion Debug"
        )
        
        if result["success"]:
            print("✅ Slide generation completed!")
            print(f"   Presentation ID: {result['presentation_id']}")
            print(f"   Web view link: {result['web_view_link']}")
            
            # Kiểm tra kết quả
            slides_created = result.get('slides_created', 0)
            print(f"   Slides created: {slides_created}")
            
            print(f"\n🔗 Check the result: {result['web_view_link']}")
            print("   Verify that only content slides remain (no template slides)")
            
        else:
            print(f"❌ Slide generation failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Test exception:")

async def test_manual_template_copy_and_delete():
    """Test manual copy template và delete để debug"""
    
    print(f"\n🧪 Testing Manual Template Copy and Delete")
    print("=" * 50)
    
    slide_service = SlideGenerationService()
    
    if not slide_service.is_available():
        print("❌ Slide service not available")
        return
    
    template_id = "1BdGYMghAcNplveEd4oaOdBdGYMghAcNplveEd4oaOdBdGYMghAcNplveEd4oaOd"  # Example ID
    
    try:
        print("📋 Step 1: Copy template...")
        
        # Copy template
        copy_result = await slide_service.slides_service.copy_and_analyze_template(
            template_id,
            "Manual Test - Template Copy"
        )
        
        if not copy_result["success"]:
            print(f"❌ Failed to copy template: {copy_result.get('error')}")
            return
            
        presentation_id = copy_result["copied_presentation_id"]
        template_slides = copy_result.get("slides", [])
        template_slide_ids = [slide.get("slideId") for slide in template_slides]
        
        print(f"✅ Template copied successfully!")
        print(f"   Presentation ID: {presentation_id}")
        print(f"   Template slide IDs: {template_slide_ids}")
        print(f"   Web link: {copy_result['web_view_link']}")
        
        print(f"\n📊 Step 2: Debug presentation state...")
        
        # Debug trạng thái ban đầu
        await slide_service.slides_service.debug_presentation_state(
            presentation_id,
            "Initial state after copy"
        )
        
        print(f"\n🔄 Step 3: Create some new slides...")
        
        # Tạo 1 slide mới để test
        if template_slides:
            base_slide_id = template_slides[0].get("slideId")
            new_slide_id = "test_new_slide_001"
            
            requests = [{
                'duplicateObject': {
                    'objectId': base_slide_id,
                    'objectIds': {
                        base_slide_id: new_slide_id
                    }
                }
            }]
            
            slide_service.slides_service.slides_service.presentations().batchUpdate(
                presentationId=presentation_id,
                body={'requests': requests}
            ).execute()
            
            print(f"✅ Created new slide: {new_slide_id}")
            
            # Debug trạng thái sau khi tạo slide mới
            await slide_service.slides_service.debug_presentation_state(
                presentation_id,
                "After creating new slide"
            )
        
        print(f"\n🗑️ Step 4: Delete template slides...")
        
        # Xóa template slides
        delete_result = await slide_service.slides_service.delete_all_template_slides(
            presentation_id,
            template_slide_ids
        )
        
        print(f"📊 Delete result: {delete_result}")
        
        if delete_result.get("success"):
            print("✅ Template slides deleted successfully!")
        else:
            print(f"❌ Template slides deletion failed: {delete_result.get('error')}")
        
        # Debug trạng thái cuối cùng
        await slide_service.slides_service.debug_presentation_state(
            presentation_id,
            "Final state after deletion"
        )
        
        print(f"\n🔗 Final result: https://docs.google.com/presentation/d/{presentation_id}/edit")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Test exception:")

if __name__ == "__main__":
    print("🧪 Starting Simple Deletion Tests")
    print("=" * 50)
    
    # Test manual copy and delete first
    asyncio.run(test_manual_template_copy_and_delete())
    
    # Then test full slide generation
    # asyncio.run(test_simple_slide_generation())
    
    print("\n✅ All simple deletion tests completed!")
