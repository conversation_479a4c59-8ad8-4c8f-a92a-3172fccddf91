#!/usr/bin/env python3
"""
Test script để kiểm tra logic duplicate slide khi không tìm thấy exact match
"""

import asyncio
import sys
import os
import logging

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.slide_generation_service import SlideGenerationService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_duplicate_logic():
    """Test logic duplicate slide khi không tìm thấy exact match"""
    
    print("🧪 Testing Duplicate Logic")
    print("=" * 40)
    
    slide_service = SlideGenerationService()
    
    # <PERSON><PERSON> analyzed template (2 slides với các placeholder khác nhau)
    analyzed_template = {
        "slides": [
            {
                "slideId": "template_slide_1",
                "elements": [
                    {"objectId": "title_1", "Type": "TitleName", "max_length": 100},
                    {"objectId": "content_1", "Type": "SubtitleContent", "max_length": 500}
                ]
            },
            {
                "slideId": "template_slide_2", 
                "elements": [
                    {"objectId": "title_2", "Type": "TitleName", "max_length": 100},
                    {"objectId": "subtitle_2", "Type": "SubtitleContent", "max_length": 300},
                    {"objectId": "content_2", "Type": "SubtitleContent", "max_length": 500}
                ]
            }
        ]
    }
    
    # Mock parsed content với slide cần placeholder không có trong template
    parsed_content = {
        "parsed_data": {
            "TitleName": [
                {"content": "Slide 1: Giới thiệu hệ tiêu hóa"},
                {"content": "Slide 2: Cấu trúc ống tiêu hóa"},
                {"content": "Slide 3: Hình ảnh minh họa"}
            ],
            "SubtitleContent": [
                {"content": "Hệ thống tiêu hóa là hệ thống quan trọng..."},
                {"content": "Ống tiêu hóa bao gồm miệng, thực quản..."},
                {"content": "Xem hình ảnh dưới đây để hiểu rõ hơn..."}
            ],
            "ImageContent": [
                {"content": "Hình 1: Sơ đồ hệ tiêu hóa"}
            ]
        },
        "slide_summaries": [
            {
                "slide_number": 1,
                "placeholders": ["TitleName", "SubtitleContent"],
                "placeholder_counts": {"TitleName": 1, "SubtitleContent": 1}
            },
            {
                "slide_number": 2,
                "placeholders": ["TitleName", "SubtitleContent"],
                "placeholder_counts": {"TitleName": 1, "SubtitleContent": 2}
            },
            {
                "slide_number": 3,
                "placeholders": ["TitleName", "SubtitleContent", "ImageContent"],
                "placeholder_counts": {"TitleName": 1, "SubtitleContent": 1, "ImageContent": 1}
            }
        ]
    }
    
    print(f"📋 Mock Data Setup:")
    print(f"   Template slides: {len(analyzed_template['slides'])}")
    print(f"   Content slides needed: {len(parsed_content['slide_summaries'])}")
    print(f"   Slide 3 needs ImageContent (not in template) -> should duplicate")
    
    try:
        # Test mapping logic
        print(f"\n🔧 Testing mapping logic with duplicate scenario...")
        
        result = asyncio.run(slide_service._map_parsed_content_to_slides(
            parsed_content,
            analyzed_template
        ))
        
        if result["success"]:
            slides = result["slides"]
            original_template_ids = result.get("original_template_slide_ids", [])
            
            print(f"✅ Mapping completed successfully!")
            print(f"   Mapped slides: {len(slides)}")
            print(f"   Original template IDs: {original_template_ids}")
            
            # Analyze results
            print(f"\n📊 Mapping Results Analysis:")
            
            action_counts = {}
            reuse_count = 0
            duplicate_count = 0
            
            for i, slide in enumerate(slides):
                slide_id = slide.get('slideId')
                action = slide.get('action', 'update')
                slide_order = slide.get('slide_order', 'N/A')
                is_reuse = slide.get('is_template_reuse', False)
                is_duplicate = slide.get('is_duplicated', False)
                original_template = slide.get('original_template_id', 'N/A')
                elements_count = len(slide.get('elements', []))
                
                action_counts[action] = action_counts.get(action, 0) + 1
                
                if is_reuse:
                    reuse_count += 1
                if is_duplicate:
                    duplicate_count += 1
                
                status = []
                if is_reuse:
                    status.append("REUSE")
                if is_duplicate:
                    status.append("DUPLICATE")
                if not status:
                    status.append("ORIGINAL")
                
                print(f"   {i+1}. {slide_id}")
                print(f"      Order: {slide_order}, Action: {action}")
                print(f"      Status: {', '.join(status)}")
                print(f"      Original template: {original_template}")
                print(f"      Elements: {elements_count}")
                
                # Show element details for duplicated slide
                if is_duplicate:
                    print(f"      Element details:")
                    for elem in slide.get('elements', []):
                        obj_id = elem.get('objectId')
                        elem_type = elem.get('Type')
                        text = elem.get('text', '')[:50] + '...' if len(elem.get('text', '')) > 50 else elem.get('text', '')
                        print(f"        - {obj_id} ({elem_type}): {text}")
            
            print(f"\n📈 Summary:")
            print(f"   Action breakdown: {action_counts}")
            print(f"   Template reuses: {reuse_count}")
            print(f"   Duplicated slides: {duplicate_count}")
            
            # Verify order
            slide_orders = [s.get('slide_order', 999) for s in slides]
            is_ordered = slide_orders == sorted(slide_orders)
            print(f"   Slides in correct order: {'✅' if is_ordered else '❌'}")
            print(f"   Slide orders: {slide_orders}")
            
            # Check if slide 3 was duplicated
            slide_3 = next((s for s in slides if s.get('slide_order') == 3), None)
            if slide_3 and slide_3.get('is_duplicated'):
                print(f"   ✅ Slide 3 correctly duplicated when no exact match found")
            else:
                print(f"   ❌ Slide 3 should have been duplicated")
            
        else:
            print(f"❌ Mapping failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Test exception:")

def test_create_duplicated_slide():
    """Test method _create_duplicated_slide trực tiếp"""
    
    print(f"\n🔄 Testing _create_duplicated_slide method")
    print("=" * 45)
    
    slide_service = SlideGenerationService()
    
    # Mock template slide
    template_slide = {
        "slideId": "template_slide_1",
        "elements": [
            {"objectId": "title_1", "Type": "TitleName", "max_length": 100},
            {"objectId": "content_1", "Type": "SubtitleContent", "max_length": 500}
        ]
    }
    
    # Mock parsed data
    parsed_data = {
        "TitleName": [
            {"content": "Test Title"}
        ],
        "SubtitleContent": [
            {"content": "Test Content"}
        ],
        "ImageContent": [
            {"content": "Test Image"}
        ]
    }
    
    content_index = {"TitleName": 0, "SubtitleContent": 0, "ImageContent": 0}
    
    try:
        print(f"🔧 Creating duplicated slide...")
        
        result = asyncio.run(slide_service._create_duplicated_slide(
            template_slide,
            parsed_data,
            content_index,
            3  # slide_order
        ))
        
        if result:
            print(f"✅ Duplicated slide created successfully!")
            print(f"   Slide ID: {result.get('slideId')}")
            print(f"   Action: {result.get('action')}")
            print(f"   Base slide ID: {result.get('baseSlideId')}")
            print(f"   Elements: {len(result.get('elements', []))}")
            
            # Show element mapping
            print(f"   Element mapping:")
            for elem in result.get('elements', []):
                obj_id = elem.get('objectId')
                elem_type = elem.get('Type')
                text = elem.get('text', '')
                print(f"     - {obj_id} ({elem_type}): {text}")
            
        else:
            print(f"❌ Failed to create duplicated slide")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Test exception:")

if __name__ == "__main__":
    print("🧪 Starting Duplicate Logic Tests")
    print("=" * 50)
    
    test_create_duplicated_slide()
    test_duplicate_logic()
    
    print("\n✅ All duplicate tests completed!")
