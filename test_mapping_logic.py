#!/usr/bin/env python3
"""
Test script để kiểm tra logic mapping mới với reuse và duplicate
"""

import asyncio
import sys
import os
import logging

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.slide_generation_service import SlideGenerationService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_mapping_logic():
    """Test logic mapping với mock data"""
    
    print("🧪 Testing Mapping Logic with Mock Data")
    print("=" * 50)
    
    slide_service = SlideGenerationService()
    
    # Mock analyzed template (2 slides với các placeholder khác nhau)
    analyzed_template = {
        "slides": [
            {
                "slideId": "template_slide_1",
                "elements": [
                    {"objectId": "title_1", "Type": "TitleName", "max_length": 100},
                    {"objectId": "content_1", "Type": "SubtitleContent", "max_length": 500}
                ]
            },
            {
                "slideId": "template_slide_2", 
                "elements": [
                    {"objectId": "title_2", "Type": "TitleName", "max_length": 100},
                    {"objectId": "subtitle_2", "Type": "SubtitleContent", "max_length": 300},
                    {"objectId": "content_2", "Type": "SubtitleContent", "max_length": 500}
                ]
            }
        ]
    }
    
    # Mock parsed content (nhiều slide hơn template)
    parsed_content = {
        "parsed_data": {
            "TitleName": [
                {"content": "Slide 1: Giới thiệu hệ tiêu hóa"},
                {"content": "Slide 2: Cấu trúc ống tiêu hóa"},
                {"content": "Slide 3: Quá trình tiêu hóa"},
                {"content": "Slide 4: Các bệnh tiêu hóa"},
                {"content": "Slide 5: Chăm sóc sức khỏe"}
            ],
            "SubtitleContent": [
                {"content": "Hệ thống tiêu hóa là hệ thống quan trọng..."},
                {"content": "Ống tiêu hóa bao gồm miệng, thực quản..."},
                {"content": "Tiêu hóa diễn ra qua hai giai đoạn..."},
                {"content": "Các bệnh phổ biến về tiêu hóa..."},
                {"content": "Cần có chế độ ăn uống hợp lý..."},
                {"content": "Tập thể dục đều đặn giúp tiêu hóa tốt..."},
                {"content": "Vệ sinh thực phẩm rất quan trọng..."}
            ]
        },
        "slide_summaries": [
            {
                "slide_number": 1,
                "placeholders": ["TitleName", "SubtitleContent"],
                "placeholder_counts": {"TitleName": 1, "SubtitleContent": 1}
            },
            {
                "slide_number": 2,
                "placeholders": ["TitleName", "SubtitleContent"],
                "placeholder_counts": {"TitleName": 1, "SubtitleContent": 2}
            },
            {
                "slide_number": 3,
                "placeholders": ["TitleName", "SubtitleContent"],
                "placeholder_counts": {"TitleName": 1, "SubtitleContent": 1}
            },
            {
                "slide_number": 4,
                "placeholders": ["TitleName", "SubtitleContent"],
                "placeholder_counts": {"TitleName": 1, "SubtitleContent": 1}
            },
            {
                "slide_number": 5,
                "placeholders": ["TitleName", "SubtitleContent"],
                "placeholder_counts": {"TitleName": 1, "SubtitleContent": 1}
            }
        ]
    }
    
    print(f"📋 Mock Data Setup:")
    print(f"   Template slides: {len(analyzed_template['slides'])}")
    print(f"   Content slides needed: {len(parsed_content['slide_summaries'])}")
    print(f"   TitleName content: {len(parsed_content['parsed_data']['TitleName'])}")
    print(f"   SubtitleContent content: {len(parsed_content['parsed_data']['SubtitleContent'])}")
    
    try:
        # Test mapping logic
        print(f"\n🔧 Testing mapping logic...")
        
        result = asyncio.run(slide_service._parse_and_map_content_to_template(
            "mock_annotated_content",  # This won't be used since we provide parsed_content directly
            analyzed_template
        ))
        
        # Override with our mock parsed content for testing
        result = asyncio.run(slide_service._map_parsed_content_to_slides(
            parsed_content,
            analyzed_template
        ))
        
        if result["success"]:
            slides = result["slides"]
            original_template_ids = result.get("original_template_slide_ids", [])
            
            print(f"✅ Mapping completed successfully!")
            print(f"   Mapped slides: {len(slides)}")
            print(f"   Original template IDs: {original_template_ids}")
            
            # Analyze results
            print(f"\n📊 Mapping Results Analysis:")
            
            action_counts = {}
            reuse_count = 0
            duplicate_count = 0
            
            for i, slide in enumerate(slides):
                slide_id = slide.get('slideId')
                action = slide.get('action', 'update')
                slide_order = slide.get('slide_order', 'N/A')
                is_reuse = slide.get('is_template_reuse', False)
                is_duplicate = slide.get('is_duplicated', False)
                original_template = slide.get('original_template_id', 'N/A')
                elements_count = len(slide.get('elements', []))
                
                action_counts[action] = action_counts.get(action, 0) + 1
                
                if is_reuse:
                    reuse_count += 1
                if is_duplicate:
                    duplicate_count += 1
                
                status = []
                if is_reuse:
                    status.append("REUSE")
                if is_duplicate:
                    status.append("DUPLICATE")
                if not status:
                    status.append("ORIGINAL")
                
                print(f"   {i+1}. {slide_id}")
                print(f"      Order: {slide_order}, Action: {action}")
                print(f"      Status: {', '.join(status)}")
                print(f"      Original template: {original_template}")
                print(f"      Elements: {elements_count}")
            
            print(f"\n📈 Summary:")
            print(f"   Action breakdown: {action_counts}")
            print(f"   Template reuses: {reuse_count}")
            print(f"   Duplicated slides: {duplicate_count}")
            
            # Verify order
            slide_orders = [s.get('slide_order', 999) for s in slides]
            is_ordered = slide_orders == sorted(slide_orders)
            print(f"   Slides in correct order: {'✅' if is_ordered else '❌'}")
            print(f"   Slide orders: {slide_orders}")
            
        else:
            print(f"❌ Mapping failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Test exception:")

def test_exact_matching_with_reuse():
    """Test exact matching logic với reuse"""
    
    print(f"\n🎯 Testing Exact Matching with Reuse")
    print("=" * 40)
    
    slide_service = SlideGenerationService()
    
    # Mock template slides
    template_slides = [
        {
            "slideId": "template_1",
            "elements": [
                {"Type": "TitleName"},
                {"Type": "SubtitleContent"}
            ]
        },
        {
            "slideId": "template_2",
            "elements": [
                {"Type": "TitleName"},
                {"Type": "SubtitleContent"},
                {"Type": "SubtitleContent"}
            ]
        }
    ]
    
    # Test cases
    test_cases = [
        {
            "name": "Exact match for template_1",
            "required_placeholders": ["TitleName", "SubtitleContent"],
            "required_counts": {"TitleName": 1, "SubtitleContent": 1},
            "expected_match": "template_1"
        },
        {
            "name": "Exact match for template_2", 
            "required_placeholders": ["TitleName", "SubtitleContent"],
            "required_counts": {"TitleName": 1, "SubtitleContent": 2},
            "expected_match": "template_2"
        },
        {
            "name": "No exact match",
            "required_placeholders": ["TitleName", "SubtitleContent"],
            "required_counts": {"TitleName": 2, "SubtitleContent": 1},
            "expected_match": None
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 Test: {test_case['name']}")
        
        result = slide_service._find_exact_matching_slide_with_reuse(
            test_case["required_placeholders"],
            test_case["required_counts"],
            template_slides
        )
        
        matched_id = result.get("slideId") if result else None
        expected = test_case["expected_match"]
        
        if matched_id == expected:
            print(f"   ✅ PASS: Found {matched_id}")
        else:
            print(f"   ❌ FAIL: Expected {expected}, got {matched_id}")

if __name__ == "__main__":
    print("🧪 Starting Mapping Logic Tests")
    print("=" * 50)
    
    test_exact_matching_with_reuse()
    test_mapping_logic()
    
    print("\n✅ All mapping tests completed!")
