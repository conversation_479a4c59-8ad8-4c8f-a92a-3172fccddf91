#!/usr/bin/env python3
"""
Test script để debug vấn đề xóa template slides
"""

import asyncio
import sys
import os
import logging

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.google_slides_service import GoogleSlidesService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_slide_deletion_debug():
    """Test và debug vấn đề xóa slides"""
    
    print("🧪 Testing Slide Deletion Debug")
    print("=" * 40)
    
    slides_service = GoogleSlidesService()
    
    if not slides_service.is_available():
        print("❌ Google Slides service not available")
        return
    
    # Test với presentation ID thực tế (thay bằng ID thực)
    test_presentation_id = "1BdGYMghAcNplveEd4oaOdBdGYMghAcNplveEd4oaOdBdGYMghAcNplveEd4oaOd"  # Example ID
    
    try:
        print(f"🔍 Step 1: Analyzing current presentation...")
        
        # Lấy thông tin presentation hiện tại
        presentation = slides_service.slides_service.presentations().get(
            presentationId=test_presentation_id
        ).execute()
        
        current_slides = presentation.get('slides', [])
        print(f"📊 Current presentation info:")
        print(f"   Title: {presentation.get('title', 'Untitled')}")
        print(f"   Total slides: {len(current_slides)}")
        
        # List tất cả slides hiện tại
        print(f"\n📋 Current slides:")
        for i, slide in enumerate(current_slides):
            slide_id = slide.get('objectId')
            print(f"   {i+1}. {slide_id}")
        
        # Test case: Giả sử muốn xóa slide đầu tiên (nếu có nhiều hơn 1 slide)
        if len(current_slides) > 1:
            slide_to_delete = current_slides[0].get('objectId')
            print(f"\n🗑️ Step 2: Testing deletion of first slide: {slide_to_delete}")
            
            delete_result = await slides_service.delete_all_template_slides(
                test_presentation_id,
                [slide_to_delete]
            )
            
            print(f"📊 Deletion result: {delete_result}")
            
            if delete_result.get("success"):
                print("✅ Deletion successful!")
                
                # Kiểm tra lại presentation sau khi xóa
                updated_presentation = slides_service.slides_service.presentations().get(
                    presentationId=test_presentation_id
                ).execute()
                
                updated_slides = updated_presentation.get('slides', [])
                print(f"\n📋 Updated slides after deletion:")
                print(f"   Total slides: {len(updated_slides)}")
                for i, slide in enumerate(updated_slides):
                    slide_id = slide.get('objectId')
                    print(f"   {i+1}. {slide_id}")
                    
            else:
                print(f"❌ Deletion failed: {delete_result.get('error', 'Unknown error')}")
                
        else:
            print("⚠️ Only 1 slide in presentation - cannot test deletion (Google Slides requires at least 1 slide)")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Test exception:")

async def test_template_copy_and_delete():
    """Test toàn bộ luồng copy template và xóa"""
    
    print(f"\n🧪 Testing Full Copy and Delete Flow")
    print("=" * 45)
    
    slides_service = GoogleSlidesService()
    
    if not slides_service.is_available():
        print("❌ Google Slides service not available")
        return
    
    # Template ID (thay bằng ID thực tế)
    template_id = "1BdGYMghAcNplveEd4oaOdBdGYMghAcNplveEd4oaOdBdGYMghAcNplveEd4oaOd"  # Example ID
    
    try:
        print(f"🔍 Step 1: Copying template...")
        
        # Copy template
        copy_result = await slides_service.copy_and_analyze_template(
            template_id, 
            "Test Copy for Deletion"
        )
        
        if not copy_result["success"]:
            print(f"❌ Failed to copy template: {copy_result.get('error')}")
            return
            
        copied_presentation_id = copy_result["copied_presentation_id"]
        original_slides = copy_result.get("slides", [])
        
        print(f"✅ Template copied successfully!")
        print(f"   Copied presentation ID: {copied_presentation_id}")
        print(f"   Original slides count: {len(original_slides)}")
        
        # Lưu template slide IDs
        template_slide_ids = [slide.get("slideId") for slide in original_slides]
        print(f"   Template slide IDs: {template_slide_ids}")
        
        print(f"\n🔍 Step 2: Simulating slide creation...")
        
        # Giả lập tạo slides mới (duplicate từ template)
        if original_slides:
            first_template = original_slides[0]
            base_slide_id = first_template.get("slideId")
            
            # Tạo 2 slides mới
            requests = []
            new_slide_ids = []
            
            for i in range(2):
                new_slide_id = f"new_slide_{i+1}"
                new_slide_ids.append(new_slide_id)
                
                requests.append({
                    'duplicateObject': {
                        'objectId': base_slide_id,
                        'objectIds': {
                            base_slide_id: new_slide_id
                        }
                    }
                })
            
            # Thực hiện tạo slides mới
            slides_service.slides_service.presentations().batchUpdate(
                presentationId=copied_presentation_id,
                body={'requests': requests}
            ).execute()
            
            print(f"✅ Created {len(new_slide_ids)} new slides: {new_slide_ids}")
        
        print(f"\n🔍 Step 3: Checking presentation before deletion...")
        
        # Kiểm tra presentation trước khi xóa
        presentation = slides_service.slides_service.presentations().get(
            presentationId=copied_presentation_id
        ).execute()
        
        current_slides = presentation.get('slides', [])
        current_slide_ids = [slide.get('objectId') for slide in current_slides]
        
        print(f"   Current slides ({len(current_slides)} total): {current_slide_ids}")
        print(f"   Template slides to delete: {template_slide_ids}")
        
        print(f"\n🗑️ Step 4: Deleting template slides...")
        
        # Xóa template slides
        delete_result = await slides_service.delete_all_template_slides(
            copied_presentation_id,
            template_slide_ids
        )
        
        print(f"📊 Deletion result: {delete_result}")
        
        if delete_result.get("success"):
            print("✅ Template slides deletion successful!")
            
            # Kiểm tra lại presentation sau khi xóa
            final_presentation = slides_service.slides_service.presentations().get(
                presentationId=copied_presentation_id
            ).execute()
            
            final_slides = final_presentation.get('slides', [])
            final_slide_ids = [slide.get('objectId') for slide in final_slides]
            
            print(f"\n📋 Final presentation state:")
            print(f"   Final slides ({len(final_slides)} total): {final_slide_ids}")
            print(f"   Slides deleted: {delete_result.get('slides_deleted', 0)}")
            print(f"   Slides not found: {delete_result.get('slides_not_found', [])}")
            
        else:
            print(f"❌ Template slides deletion failed: {delete_result.get('error', 'Unknown error')}")
        
        print(f"\n🔗 Test presentation link: https://docs.google.com/presentation/d/{copied_presentation_id}/edit")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Test exception:")

if __name__ == "__main__":
    print("🧪 Starting Slide Deletion Debug Tests")
    print("=" * 50)
    
    # Uncomment the test you want to run
    # asyncio.run(test_slide_deletion_debug())
    asyncio.run(test_template_copy_and_delete())
    
    print("\n✅ All deletion debug tests completed!")
