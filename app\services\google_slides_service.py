import logging
import threading
import os
import json
from typing import Dict, Any, List, Optional
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from app.core.config import settings

logger = logging.getLogger(__name__)


def extract_text_from_shape(shape: Dict[str, Any]) -> str:
    if 'text' in shape and 'textElements' in shape['text']:
        parts = []
        for te in shape['text']['textElements']:
            text_run = te.get('textRun')
            if text_run:
                parts.append(text_run.get('content', '').strip())
        return ' '.join(parts).strip()
    return ''


def extract_text_style_from_shape(shape: Dict[str, Any], presentation: Dict[str, Any] = None) -> Dict[str, Any]:
    """Extract text style information including font size, family, etc."""
    text_styles = []
    placeholder_info = {}

    # Get placeholder information if available
    if 'placeholder' in shape:
        placeholder = shape['placeholder']
        placeholder_info = {
            'type': placeholder.get('type'),
            'parentObjectId': placeholder.get('parentObjectId')
        }

    if 'text' in shape and 'textElements' in shape['text']:
        for te in shape['text']['textElements']:
            text_run = te.get('textRun')
            if text_run:
                style = text_run.get('style', {})
                text_styles.append({
                    'content': text_run.get('content', '').strip(),
                    'style': style,
                    'hasStyle': bool(style)  # Track if style exists
                })

    # Return consolidated style info
    result = {
        'placeholder': placeholder_info if placeholder_info else None,
        'textElements': text_styles
    }

    # If we have styles, extract common properties
    styles_with_data = [ts for ts in text_styles if ts['hasStyle']]
    if styles_with_data:
        first_style = styles_with_data[0]['style']
        result.update({
            'fontSize': first_style.get('fontSize'),
            'fontFamily': first_style.get('fontFamily'),
            'bold': first_style.get('bold'),
            'italic': first_style.get('italic'),
            'underline': first_style.get('underline'),
            'foregroundColor': first_style.get('foregroundColor'),
            'backgroundColor': first_style.get('backgroundColor')
        })
    else:
        # No explicit styles found - try to get from parent placeholder
        inherited_style = {}
        if presentation and placeholder_info.get('parentObjectId'):
            inherited_style = find_parent_placeholder_style(
                presentation,
                placeholder_info['parentObjectId']
            )

        # Chỉ lấy giá trị thực từ API, không tạo default
        result.update({
            'fontSize': inherited_style.get('fontSize'),
            'fontFamily': inherited_style.get('fontFamily'),
            'bold': inherited_style.get('bold'),
            'italic': inherited_style.get('italic'),
            'underline': inherited_style.get('underline'),
            'foregroundColor': inherited_style.get('foregroundColor'),
            'backgroundColor': inherited_style.get('backgroundColor'),
            'note': 'Styles inherited from placeholder parent' if inherited_style else 'No explicit styles found in placeholder parent'
        })

    return result


def find_parent_placeholder_style(presentation: Dict[str, Any], parent_object_id: str) -> Dict[str, Any]:
    """Find and extract text style from parent placeholder"""

    def extract_style_from_element(element):
        """Extract style from a single element"""
        if 'shape' not in element:
            return {}

        shape = element['shape']
        if 'text' not in shape or 'textElements' not in shape['text']:
            return {}

        # Collect all styles from text elements and merge them
        merged_style = {}
        for te in shape['text']['textElements']:
            text_run = te.get('textRun')
            if text_run and 'style' in text_run:
                style = text_run['style']
                if style:  # Non-empty style
                    # Merge styles, prioritizing properties that exist
                    for key, value in style.items():
                        if key not in merged_style:
                            merged_style[key] = value

        return merged_style

    # Search in layouts first
    for layout in presentation.get('layouts', []):
        for element in layout.get('pageElements', []):
            if element.get('objectId') == parent_object_id:
                style = extract_style_from_element(element)
                if style:
                    return style

    # Search in masters if not found in layouts
    for master in presentation.get('masters', []):
        for element in master.get('pageElements', []):
            if element.get('objectId') == parent_object_id:
                style = extract_style_from_element(element)
                if style:
                    return style

    # Nếu không tìm thấy style từ parent placeholder, trả về empty dict
    return {}


class GoogleSlidesService:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(GoogleSlidesService, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return
        self.slides_service = None
        self.drive_service = None
        self.credentials = None
        self._service_initialized = False
        self._initialized = True

    def _ensure_service_initialized(self):
        if not self._service_initialized:
            logger.info("🔄 GoogleSlidesService: First-time initialization triggered")
            self._initialize_service()
            self._service_initialized = True
            logger.info("✅ GoogleSlidesService: Initialization completed")

    def _initialize_service(self):
        """Khởi tạo Google Slides service với OAuth 2.0"""
        try:
            credentials_path = "google_client_Dat.json"
            token_path = "token.json"  # File lưu token sau khi authenticate

            if not os.path.exists(credentials_path):
                logger.warning("""
Google Slides service requires OAuth 2.0 Client credentials.
Please ensure google_client_Dat.json exists in the project root.
Service will be disabled.
                """)
                return

            # Scopes cần thiết
            SCOPES = [
                'https://www.googleapis.com/auth/drive',
                'https://www.googleapis.com/auth/presentations',
                'https://www.googleapis.com/auth/drive.file',
                'https://www.googleapis.com/auth/gmail.modify'
            ]

            creds = None

            # Kiểm tra xem đã có token chưa
            if os.path.exists(token_path):
                creds = Credentials.from_authorized_user_file(token_path, SCOPES)

            # Nếu không có credentials hợp lệ, thực hiện OAuth flow
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    # Refresh token nếu expired
                    creds.refresh(Request())
                else:
                    # Thực hiện OAuth flow mới
                    flow = InstalledAppFlow.from_client_secrets_file(
                        credentials_path, SCOPES
                    )
                    # Sử dụng local server để nhận callback
                    creds = flow.run_local_server()

                # Lưu token để sử dụng lần sau
                with open(token_path, 'w') as token:
                    token.write(creds.to_json())

            self.credentials = creds

            # Tạo services
            self.slides_service = build('slides', 'v1', credentials=self.credentials)
            self.drive_service = build('drive', 'v3', credentials=self.credentials)
            logger.info("Google Slides service initialized with OAuth 2.0")

        except Exception as e:
            logger.error(f"Failed to initialize Google Slides service: {e}")
            self.slides_service = None
            self.drive_service = None

    def is_available(self) -> bool:
        self._ensure_service_initialized()
        return self.slides_service is not None and self.drive_service is not None

    async def copy_and_analyze_template(self, template_id: str, new_title: str) -> Dict[str, Any]:
        """
        Copy template và phân tích cấu trúc của bản sao (theo yêu cầu mới)

        Args:
            template_id: ID của Google Slides template gốc
            new_title: Tên cho file mới

        Returns:
            Dict chứa thông tin file đã copy và cấu trúc slides/elements
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            # Bước 1: Copy template thành file mới ngay từ đầu
            logger.info(f"Copying template {template_id} to new file: {new_title}")
            copy_result = await self.copy_template(template_id, new_title)
            if not copy_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to copy template: {copy_result['error']}"
                }

            copied_presentation_id = copy_result["file_id"]
            logger.info(f"Template copied successfully. New presentation ID: {copied_presentation_id}")

            # Bước 2: Phân tích cấu trúc của bản sao (không phải template gốc)
            presentation = self.slides_service.presentations().get(
                presentationId=copied_presentation_id
            ).execute()

            slides_info = []
            for slide in presentation.get('slides', []):
                slide_info = {
                    "slideId": slide.get("objectId"),
                    "elements": []
                }

                for element in slide.get('pageElements', []):
                    if 'shape' in element:
                        text = extract_text_from_shape(element['shape'])
                        

                        element_info = {
                            "objectId": element.get('objectId'),
                            "text": text,
                        
                        }

                        slide_info['elements'].append(element_info)

                slides_info.append(slide_info)

            return {
                "success": True,
                "original_template_id": template_id,
                "copied_presentation_id": copied_presentation_id,
                "presentation_title": presentation.get('title', 'Untitled'),
                "web_view_link": copy_result["web_view_link"],
                "slide_count": len(presentation.get('slides', [])),
                "slides": slides_info
            }

        except HttpError as e:
            logger.error(f"HTTP error in copy_and_analyze_template: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error in copy_and_analyze_template: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def analyze_template_structure(self, template_id: str) -> Dict[str, Any]:
        """
        Phân tích cấu trúc template Google Slides (lấy text trên các slide)
        DEPRECATED: Sử dụng copy_and_analyze_template thay thế

        Args:
            template_id: ID của Google Slides template

        Returns:
            Dict chứa thông tin slides, objectId và text đang hiển thị
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            presentation = self.slides_service.presentations().get(
                presentationId=template_id
            ).execute()

            slides_info = []
            for slide in presentation.get('slides', []):
                slide_info = {
                    "slideId": slide.get("objectId"),
                    "elements": []
                }

                for element in slide.get('pageElements', []):
                    if 'shape' in element:
                        text = extract_text_from_shape(element['shape'])
                        element_info = {
                            "objectId": element.get('objectId'),
                            "text": text,
                              
                        }
                
                        slide_info['elements'].append(element_info)

                slides_info.append(slide_info)


            return {
                "success": True,
                "template_id": template_id,
                "title": presentation.get('title', 'Untitled'),
                "slide_count": len(presentation.get('slides', [])),
                "slides": slides_info
            }

        except HttpError as e:
            logger.error(f"HTTP error analyzing template {template_id}: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error analyzing template {template_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def copy_template(self, template_id: str, new_title: str) -> Dict[str, Any]:
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            copied_file = self.drive_service.files().copy(
                fileId=template_id,
                body={'name': new_title}
            ).execute()

            self.drive_service.permissions().create(
            fileId=copied_file.get('id'),
            body={
                'type': 'anyone',
                'role': 'writer'
            },
            fields='id',
            supportsAllDrives=True,
            sendNotificationEmail=False
            ).execute()


            return {
                "success": True,
                "file_id": copied_file.get('id'),
                "name": copied_file.get('name'),
                "web_view_link": f"https://docs.google.com/presentation/d/{copied_file.get('id')}/edit"
            }

        except HttpError as e:
            logger.error(f"HTTP error copying template {template_id}: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error copying template {template_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def update_copied_presentation_content(
        self,
        presentation_id: str,
        slides_content: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Cập nhật nội dung vào presentation đã copy (theo quy trình mới)

        Args:
            presentation_id: ID của presentation đã copy
            slides_content: List nội dung slides từ LLM

        Returns:
            Dict kết quả cập nhật nội dung
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            requests = []

            logger.info(f"📝 Updating presentation {presentation_id} with {len(slides_content)} slides")
            logger.info("🔄 Processing slides with reuse and duplicate support...")

            # Lấy thông tin presentation hiện tại
            presentation = self.slides_service.presentations().get(
                presentationId=presentation_id
            ).execute()

            logger.info(f"Current presentation has {len(presentation.get('slides', []))} slides")

            # Sắp xếp slides theo thứ tự từ LLM trước khi xử lý
            logger.info("📋 Sorting slides by LLM content order...")
            sorted_slides_content = sorted(slides_content, key=lambda x: x.get('slide_order', 999))

            # Log thứ tự slides
            for i, slide in enumerate(sorted_slides_content):
                slide_id = slide.get('slideId')
                slide_order = slide.get('slide_order', 'N/A')
                action = slide.get('action', 'update')
                logger.info(f"   {i+1}. Slide {slide_id} (order: {slide_order}, action: {action})")

            # Xử lý từng slide theo thứ tự đã sắp xếp
            slides_created = 0
            slides_updated = 0

            for slide_content in sorted_slides_content:
                slide_id = slide_content.get('slideId')
                action = slide_content.get('action', 'update')
                updates = slide_content.get('updates', {})

                if not slide_id or not updates:
                    logger.warning(f"Skipping slide with missing slideId or updates: {slide_content}")
                    continue

                logger.info(f"🔧 Processing slide {slide_id} with action '{action}' and {len(updates)} updates")

                # Xử lý tạo slide mới
                if action == 'create':
                    base_slide_id = slide_content.get('baseSlideId')
                    slide_order = slide_content.get('slide_order', slides_created + 1)

                    # Tính insertion index dựa trên slide_order để đảm bảo thứ tự đúng
                    insertion_index = slide_order - 1  # Convert to 0-based index

                    if base_slide_id:
                        # Tạo slide mới bằng cách duplicate slide base
                        requests.append({
                            'duplicateObject': {
                                'objectId': base_slide_id,
                                'objectIds': {
                                    base_slide_id: slide_id
                                }
                            }
                        })
                        slides_created += 1
                        logger.info(f"📄 Creating new slide {slide_id} based on {base_slide_id} at position {insertion_index}")
                    else:
                        # Tạo slide trống mới
                        requests.append({
                            'createSlide': {
                                'objectId': slide_id,
                                'insertionIndex': insertion_index
                            }
                        })
                        slides_created += 1
                        logger.info(f"📄 Creating new blank slide {slide_id} at position {insertion_index}")

                # Cập nhật nội dung cho slide (cả slide cũ và mới)
                for element_id, new_content in updates.items():
                    if not new_content:
                        continue

                    # Làm sạch nội dung
                    clean_content = str(new_content).strip()
                    if not clean_content:
                        continue

                    # Xóa nội dung cũ trước
                    requests.append({
                        'deleteText': {
                            'objectId': element_id,
                            'textRange': {
                                'type': 'ALL'
                            }
                        }
                    })

                    # Thêm nội dung mới
                    requests.append({
                        'insertText': {
                            'objectId': element_id,
                            'text': clean_content,
                            'insertionIndex': 0
                        }
                    })

                    logger.debug(f"Updated element {element_id} with content: {clean_content[:50]}...")

                if action == 'update':
                    slides_updated += 1

            # Thực thi tất cả requests
            if requests:
                logger.info(f"⚡ Executing {len(requests)} batch update requests")
                logger.info(f"   - Slides created: {slides_created}")
                logger.info(f"   - Slides updated: {slides_updated}")

                # Chia requests thành các batch nhỏ để tránh timeout
                batch_size = 50
                for i in range(0, len(requests), batch_size):
                    batch_requests = requests[i:i + batch_size]
                    logger.info(f"Executing batch {i//batch_size + 1}: {len(batch_requests)} requests")

                    self.slides_service.presentations().batchUpdate(
                        presentationId=presentation_id,
                        body={'requests': batch_requests}
                    ).execute()

                logger.info("✅ All batch updates completed successfully")
            else:
                logger.warning("No requests to execute")

            return {
                "success": True,
                "presentation_id": presentation_id,
                "slides_updated": slides_updated,
                "slides_created": slides_created,
                "total_slides_processed": len(slides_content),
                "requests_executed": len(requests)
            }

        except HttpError as e:
            logger.error(f"HTTP error creating slides for {presentation_id}: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error creating slides for {presentation_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def delete_unused_slides(
        self,
        presentation_id: str,
        used_slide_ids: List[str]
    ) -> Dict[str, Any]:
        """
        Xóa các slide không được sử dụng trong presentation

        Args:
            presentation_id: ID của presentation
            used_slide_ids: List các slide IDs đã được sử dụng

        Returns:
            Dict kết quả xóa slides
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            # Lấy thông tin presentation hiện tại
            presentation = self.slides_service.presentations().get(
                presentationId=presentation_id
            ).execute()

            current_slides = presentation.get('slides', [])
            slides_to_delete = []

            # Tìm slides cần xóa (không có trong used_slide_ids)
            for slide in current_slides:
                slide_id = slide.get('objectId')
                if slide_id and slide_id not in used_slide_ids:
                    slides_to_delete.append(slide_id)

            if not slides_to_delete:
                logger.info("No unused slides to delete")
                return {
                    "success": True,
                    "slides_deleted": 0,
                    "message": "No unused slides found"
                }

            # Tạo requests để xóa slides
            requests = []
            for slide_id in slides_to_delete:
                requests.append({
                    'deleteObject': {
                        'objectId': slide_id
                    }
                })

            logger.info(f"Deleting {len(slides_to_delete)} unused slides: {slides_to_delete}")

            # Thực hiện xóa
            self.slides_service.presentations().batchUpdate(
                presentationId=presentation_id,
                body={'requests': requests}
            ).execute()

            return {
                "success": True,
                "slides_deleted": len(slides_to_delete),
                "deleted_slide_ids": slides_to_delete
            }

        except HttpError as e:
            logger.error(f"HTTP error deleting unused slides: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error deleting unused slides: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def delete_template_slides(
        self,
        presentation_id: str,
        template_slide_ids: List[str],
        content_slide_ids: List[str]
    ) -> Dict[str, Any]:
        """
        Xóa chỉ các template slides gốc, giữ lại slides có content

        Args:
            presentation_id: ID của presentation
            template_slide_ids: List các template slide IDs gốc
            content_slide_ids: List các slide IDs có content thực tế

        Returns:
            Dict kết quả xóa template slides
        """
        if not self.is_available():
            return {
                "success": False,
                "error": "Google Slides service not available"
            }

        try:
            logger.info(f"🧹 Starting template slides cleanup...")
            logger.info(f"   Template slides to check: {template_slide_ids}")
            logger.info(f"   Content slides to keep: {content_slide_ids}")

            # Lấy thông tin presentation hiện tại
            presentation = self.slides_service.presentations().get(
                presentationId=presentation_id
            ).execute()

            current_slides = presentation.get('slides', [])
            current_slide_ids = [slide.get('objectId') for slide in current_slides]

            logger.info(f"   Current slides in presentation: {current_slide_ids}")

            # Tìm template slides cần xóa (template gốc không có content)
            slides_to_delete = []
            for template_id in template_slide_ids:
                # Chỉ xóa nếu:
                # 1. Template slide vẫn tồn tại trong presentation
                # 2. Không có trong danh sách content slides
                if template_id in current_slide_ids and template_id not in content_slide_ids:
                    slides_to_delete.append(template_id)
                    logger.info(f"🗑️ Template slide marked for deletion: {template_id}")
                else:
                    logger.info(f"✅ Template slide kept (has content or not found): {template_id}")

            if not slides_to_delete:
                logger.info("ℹ️ No template slides to delete")
                return {
                    "success": True,
                    "slides_deleted": 0,
                    "message": "No template slides need to be deleted"
                }

            # Tạo requests để xóa template slides
            requests = []
            for slide_id in slides_to_delete:
                requests.append({
                    'deleteObject': {
                        'objectId': slide_id
                    }
                })

            logger.info(f"🗑️ Deleting {len(slides_to_delete)} template slides: {slides_to_delete}")

            # Thực hiện xóa
            self.slides_service.presentations().batchUpdate(
                presentationId=presentation_id,
                body={'requests': requests}
            ).execute()

            logger.info(f"✅ Successfully deleted {len(slides_to_delete)} template slides")

            return {
                "success": True,
                "slides_deleted": len(slides_to_delete),
                "deleted_slide_ids": slides_to_delete,
                "kept_content_slides": content_slide_ids
            }

        except HttpError as e:
            logger.error(f"HTTP error deleting template slides: {e}")
            return {
                "success": False,
                "error": f"HTTP error: {e}"
            }
        except Exception as e:
            logger.error(f"Error deleting template slides: {e}")
            return {
                "success": False,
                "error": str(e)
            }


def get_google_slides_service() -> GoogleSlidesService:
    return GoogleSlidesService()
