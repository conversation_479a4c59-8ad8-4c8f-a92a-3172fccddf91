#!/usr/bin/env python3
"""
Script để copy slide trong Google Slides presentation
"""

import asyncio
import sys
import os
import logging

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.google_slides_service import GoogleSlidesService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def copy_slide_by_id(presentation_id: str, source_slide_id: str, new_slide_id: str = None):
    """
    Copy một slide trong presentation
    
    Args:
        presentation_id: ID của presentation
        source_slide_id: ID của slide nguồn cần copy
        new_slide_id: ID cho slide mới (nếu không có sẽ auto generate)
    """
    
    print(f"📄 Copying slide in presentation...")
    print(f"   Presentation ID: {presentation_id}")
    print(f"   Source slide ID: {source_slide_id}")
    print(f"   New slide ID: {new_slide_id or 'Auto-generated'}")
    
    slides_service = GoogleSlidesService()
    
    if not slides_service.is_available():
        print("❌ Google Slides service not available")
        return None
    
    try:
        # Nếu không có new_slide_id, tạo ID mới
        if not new_slide_id:
            import uuid
            new_slide_id = f"copied_slide_{str(uuid.uuid4())[:8]}"
        
        print(f"\n🔍 Step 1: Checking source slide exists...")
        
        # Kiểm tra presentation và slide nguồn
        presentation = slides_service.slides_service.presentations().get(
            presentationId=presentation_id
        ).execute()
        
        current_slides = presentation.get('slides', [])
        current_slide_ids = [slide.get('objectId') for slide in current_slides]
        
        print(f"   Current slides: {current_slide_ids}")
        
        if source_slide_id not in current_slide_ids:
            print(f"❌ Source slide {source_slide_id} not found in presentation")
            return None
        
        print(f"✅ Source slide found")
        
        print(f"\n🔄 Step 2: Copying slide...")
        
        # Tạo request copy slide
        requests = [{
            'duplicateObject': {
                'objectId': source_slide_id,
                'objectIds': {
                    source_slide_id: new_slide_id
                }
            }
        }]
        
        # Thực hiện copy
        result = slides_service.slides_service.presentations().batchUpdate(
            presentationId=presentation_id,
            body={'requests': requests}
        ).execute()
        
        print(f"✅ Slide copied successfully!")
        print(f"   New slide ID: {new_slide_id}")
        print(f"   API result: {result}")
        
        # Kiểm tra kết quả
        updated_presentation = slides_service.slides_service.presentations().get(
            presentationId=presentation_id
        ).execute()
        
        updated_slides = updated_presentation.get('slides', [])
        updated_slide_ids = [slide.get('objectId') for slide in updated_slides]
        
        print(f"\n📊 Updated presentation:")
        print(f"   Total slides: {len(updated_slides)}")
        print(f"   Slide IDs: {updated_slide_ids}")
        
        if new_slide_id in updated_slide_ids:
            print(f"✅ Copy verified - new slide exists")
        else:
            print(f"❌ Copy failed - new slide not found")
        
        return {
            "success": True,
            "new_slide_id": new_slide_id,
            "presentation_id": presentation_id,
            "total_slides": len(updated_slides)
        }
        
    except Exception as e:
        print(f"❌ Error copying slide: {e}")
        logger.exception("Copy slide error:")
        return None

async def copy_multiple_slides(presentation_id: str, slide_copies: list):
    """
    Copy nhiều slides cùng lúc
    
    Args:
        presentation_id: ID của presentation
        slide_copies: List các dict {"source_id": "...", "new_id": "..."}
    """
    
    print(f"📄 Copying multiple slides...")
    print(f"   Presentation ID: {presentation_id}")
    print(f"   Number of copies: {len(slide_copies)}")
    
    slides_service = GoogleSlidesService()
    
    if not slides_service.is_available():
        print("❌ Google Slides service not available")
        return None
    
    try:
        print(f"\n🔍 Step 1: Validating source slides...")
        
        # Kiểm tra presentation
        presentation = slides_service.slides_service.presentations().get(
            presentationId=presentation_id
        ).execute()
        
        current_slides = presentation.get('slides', [])
        current_slide_ids = [slide.get('objectId') for slide in current_slides]
        
        print(f"   Current slides: {current_slide_ids}")
        
        # Validate tất cả source slides
        valid_copies = []
        for copy_info in slide_copies:
            source_id = copy_info.get("source_id")
            new_id = copy_info.get("new_id")
            
            if not new_id:
                import uuid
                new_id = f"copied_slide_{str(uuid.uuid4())[:8]}"
                copy_info["new_id"] = new_id
            
            if source_id in current_slide_ids:
                valid_copies.append(copy_info)
                print(f"   ✅ {source_id} -> {new_id}")
            else:
                print(f"   ❌ {source_id} not found")
        
        if not valid_copies:
            print("❌ No valid source slides found")
            return None
        
        print(f"\n🔄 Step 2: Creating copy requests...")
        
        # Tạo requests cho tất cả copies
        requests = []
        for copy_info in valid_copies:
            source_id = copy_info["source_id"]
            new_id = copy_info["new_id"]
            
            requests.append({
                'duplicateObject': {
                    'objectId': source_id,
                    'objectIds': {
                        source_id: new_id
                    }
                }
            })
        
        print(f"   Created {len(requests)} copy requests")
        
        # Thực hiện batch copy
        result = slides_service.slides_service.presentations().batchUpdate(
            presentationId=presentation_id,
            body={'requests': requests}
        ).execute()
        
        print(f"✅ Batch copy completed!")
        print(f"   API result: {result}")
        
        # Kiểm tra kết quả
        updated_presentation = slides_service.slides_service.presentations().get(
            presentationId=presentation_id
        ).execute()
        
        updated_slides = updated_presentation.get('slides', [])
        updated_slide_ids = [slide.get('objectId') for slide in updated_slides]
        
        print(f"\n📊 Updated presentation:")
        print(f"   Total slides: {len(updated_slides)}")
        print(f"   New slide IDs: {[c['new_id'] for c in valid_copies]}")
        
        # Verify tất cả slides đã được copy
        success_count = 0
        for copy_info in valid_copies:
            new_id = copy_info["new_id"]
            if new_id in updated_slide_ids:
                success_count += 1
                print(f"   ✅ {new_id} copied successfully")
            else:
                print(f"   ❌ {new_id} copy failed")
        
        return {
            "success": success_count == len(valid_copies),
            "copied_slides": valid_copies,
            "success_count": success_count,
            "total_attempts": len(valid_copies),
            "presentation_id": presentation_id,
            "total_slides": len(updated_slides)
        }
        
    except Exception as e:
        print(f"❌ Error copying multiple slides: {e}")
        logger.exception("Copy multiple slides error:")
        return None

async def main():
    """Main function để test copy slides"""
    
    print("🧪 Google Slides Copy Script")
    print("=" * 40)
    
    # Thay đổi các giá trị này theo presentation thực tế của bạn
    PRESENTATION_ID = "1_4tAO46omZAXEAwqeTV6U0fWWBoklSI22EZ_FFf0UB0"  # Example ID
    SOURCE_SLIDE_ID = "slide_008_copy_of_p5"  # Thay bằng slide ID thực tế
    
    print("📋 Test 1: Copy single slide")
    result1 = await copy_slide_by_id(
        presentation_id=PRESENTATION_ID,
        source_slide_id=SOURCE_SLIDE_ID,
        new_slide_id="my_copied_slide_001"
    )
    
    if result1:
        print(f"✅ Single copy result: {result1}")
    
    print("\n📋 Test 2: Copy multiple slides")
    slide_copies = [
        {"source_id": SOURCE_SLIDE_ID, "new_id": "my_copied_slide_002"},
        {"source_id": SOURCE_SLIDE_ID, "new_id": "my_copied_slide_003"},
        # Thêm nhiều copies nếu cần
    ]
    
    result2 = await copy_multiple_slides(
        presentation_id=PRESENTATION_ID,
        slide_copies=slide_copies
    )
    
    if result2:
        print(f"✅ Multiple copy result: {result2}")
    
    print(f"\n🔗 Check result: https://docs.google.com/presentation/d/{PRESENTATION_ID}/edit")

if __name__ == "__main__":
    asyncio.run(main())
