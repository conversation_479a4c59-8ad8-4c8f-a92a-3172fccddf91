#!/usr/bin/env python3
"""
Test script để kiểm tra tính năng reuse template và duplicate slide mới
"""

import asyncio
import sys
import os
import logging

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.slide_generation_service import SlideGenerationService
from app.services.google_slides_service import GoogleSlidesService
from app.services.llm_service import LLMService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_slide_reuse_and_duplicate():
    """Test tính năng reuse template và duplicate slide"""
    
    print("🧪 Testing Slide Reuse and Duplicate Functionality")
    print("=" * 60)
    
    # Initialize services
    slide_service = SlideGenerationService()
    
    if not slide_service.is_available():
        print("❌ Slide service not available. Please check Google API credentials.")
        return
    
    # Test data - lesson content với nhiều slide hơn template
    lesson_content = """
    Bài học: <PERSON>ệ thống tiêu hóa con người
    
    1. Giới thiệu về hệ thống tiêu hóa
    - Chức năng chính của hệ thống tiêu hóa
    - Các cơ quan tham gia
    
    2. Cấu trúc ống tiêu hóa
    - Miệng và răng
    - Thực quản
    - Dạ dày
    
    3. Quá trình tiêu hóa thức ăn
    - Tiêu hóa cơ học
    - Tiêu hóa hóa học
    - Hấp thụ chất dinh dưỡng
    
    4. Các bệnh về tiêu hóa
    - Viêm dạ dày
    - Loét dạ dày
    - Rối loạn tiêu hóa
    
    5. Cách chăm sóc hệ tiêu hóa
    - Chế độ ăn uống hợp lý
    - Tập thể dục đều đặn
    - Vệ sinh thực phẩm
    
    6. Kết luận
    - Tầm quan trọng của hệ tiêu hóa
    - Lời khuyên cho học sinh
    """
    
    # Template ID (thay bằng ID thực tế của template)
    template_id = "1BdGYMghAcNplveEd4oaOdBdGYMghAcNplveEd4oaOdBdGYMghAcNplveEd4oaOd"  # Example ID
    
    try:
        print("🚀 Starting slide generation with reuse and duplicate support...")
        
        # Test slide generation
        result = await slide_service.generate_slides_from_lesson(
            lesson_id="test_lesson_reuse_001",
            lesson_content=lesson_content,
            template_id=template_id,
            presentation_title="Test Reuse và Duplicate - Hệ thống tiêu hóa"
        )
        
        if result["success"]:
            print("✅ Slide generation completed successfully!")
            print(f"   Presentation ID: {result['presentation_id']}")
            print(f"   Slides created: {result.get('slides_created', 'N/A')}")
            print(f"   Web view link: {result['web_view_link']}")
            
            # Kiểm tra kết quả
            slides_generated = result.get('slides_generated', [])
            print(f"\n📊 Generated Slides Analysis:")
            print(f"   Total slides: {len(slides_generated)}")
            
            # Phân tích loại slides
            created_slides = [s for s in slides_generated if s.get('action') == 'create']
            updated_slides = [s for s in slides_generated if s.get('action') == 'update']
            reused_slides = [s for s in slides_generated if s.get('is_template_reuse')]
            duplicated_slides = [s for s in slides_generated if s.get('is_duplicated')]
            
            print(f"   - Created slides: {len(created_slides)}")
            print(f"   - Updated slides: {len(updated_slides)}")
            print(f"   - Reused templates: {len(reused_slides)}")
            print(f"   - Duplicated slides: {len(duplicated_slides)}")
            
            # Chi tiết từng slide
            print(f"\n📋 Slide Details:")
            for i, slide in enumerate(slides_generated):
                slide_id = slide.get('slideId')
                action = slide.get('action', 'update')
                slide_order = slide.get('slide_order', 'N/A')
                is_reuse = slide.get('is_template_reuse', False)
                is_duplicate = slide.get('is_duplicated', False)
                original_template = slide.get('original_template_id', 'N/A')
                
                status = []
                if is_reuse:
                    status.append("REUSE")
                if is_duplicate:
                    status.append("DUPLICATE")
                if not status:
                    status.append("ORIGINAL")
                
                print(f"   {i+1}. {slide_id} (order: {slide_order}, action: {action})")
                print(f"      Status: {', '.join(status)}")
                print(f"      Original template: {original_template}")
            
            print(f"\n🎉 Test completed successfully!")
            print(f"🔗 View result: {result['web_view_link']}")
            
        else:
            print(f"❌ Slide generation failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        logger.exception("Test exception details:")

async def test_template_analysis():
    """Test phân tích template để hiểu cấu trúc"""
    
    print("\n🔍 Testing Template Analysis")
    print("=" * 40)
    
    slides_service = GoogleSlidesService()
    
    if not slides_service.is_available():
        print("❌ Google Slides service not available")
        return
    
    template_id = "1BdGYMghAcNplveEd4oaOdBdGYMghAcNplveEd4oaOdBdGYMghAcNplveEd4oaOd"  # Example ID
    
    try:
        # Analyze template
        result = await slides_service.analyze_template(template_id)
        
        if result["success"]:
            print("✅ Template analysis completed!")
            print(f"   Template ID: {result['template_id']}")
            print(f"   Title: {result['title']}")
            print(f"   Slide count: {result['slide_count']}")
            
            # Analyze slides
            slides = result.get('slides', [])
            print(f"\n📊 Template Slides Analysis:")
            
            for i, slide in enumerate(slides):
                slide_id = slide.get('slideId')
                elements = slide.get('elements', [])
                
                print(f"   Slide {i+1}: {slide_id}")
                print(f"      Elements: {len(elements)}")
                
                # Analyze placeholder types
                placeholder_types = {}
                for elem in elements:
                    ptype = elem.get('Type', 'Unknown')
                    placeholder_types[ptype] = placeholder_types.get(ptype, 0) + 1
                
                print(f"      Placeholder types: {placeholder_types}")
                
        else:
            print(f"❌ Template analysis failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Template analysis failed: {e}")
        logger.exception("Template analysis exception:")

if __name__ == "__main__":
    print("🧪 Starting Slide Reuse and Duplicate Tests")
    print("=" * 60)
    
    # Run tests
    asyncio.run(test_template_analysis())
    asyncio.run(test_slide_reuse_and_duplicate())
    
    print("\n✅ All tests completed!")
