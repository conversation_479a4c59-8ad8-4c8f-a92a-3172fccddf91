#!/usr/bin/env python3
"""
Test script để kiểm tra luồng mới: copy-based template usage
"""

import asyncio
import sys
import os
import logging

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.slide_generation_service import SlideGenerationService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_new_mapping_flow():
    """Test luồng mapping mới với copy-based approach"""
    
    print("🧪 Testing New Copy-Based Mapping Flow")
    print("=" * 50)
    
    slide_service = SlideGenerationService()
    
    # <PERSON><PERSON> analyzed template (2 slides với các placeholder khác nhau)
    analyzed_template = {
        "slides": [
            {
                "slideId": "template_slide_1",
                "elements": [
                    {"objectId": "title_1", "Type": "TitleName", "max_length": 100},
                    {"objectId": "content_1", "Type": "SubtitleContent", "max_length": 500}
                ]
            },
            {
                "slideId": "template_slide_2", 
                "elements": [
                    {"objectId": "title_2", "Type": "TitleName", "max_length": 100},
                    {"objectId": "subtitle_2", "Type": "SubtitleContent", "max_length": 300},
                    {"objectId": "content_2", "Type": "SubtitleContent", "max_length": 500}
                ]
            }
        ]
    }
    
    # Mock parsed content với nhiều slides theo thứ tự chính xác
    parsed_content = {
        "parsed_data": {
            "TitleName": [
                {"content": "Slide 1: Giới thiệu hệ tiêu hóa"},
                {"content": "Slide 2: Cấu trúc ống tiêu hóa"},
                {"content": "Slide 3: Quá trình tiêu hóa cơ học"},
                {"content": "Slide 4: Quá trình tiêu hóa hóa học"},
                {"content": "Slide 5: Hấp thụ chất dinh dưỡng"},
                {"content": "Slide 6: Các bệnh về tiêu hóa"}
            ],
            "SubtitleContent": [
                {"content": "Hệ thống tiêu hóa là hệ thống quan trọng giúp cơ thể hấp thụ dinh dưỡng"},
                {"content": "Ống tiêu hóa bao gồm miệng, thực quản, dạ dày và ruột"},
                {"content": "Tiêu hóa cơ học là quá trình nghiền nát thức ăn bằng răng"},
                {"content": "Tiêu hóa hóa học sử dụng enzyme để phân giải thức ăn"},
                {"content": "Chất dinh dưỡng được hấp thụ qua thành ruột non"},
                {"content": "Các bệnh phổ biến: viêm dạ dày, loét dạ dày, rối loạn tiêu hóa"},
                {"content": "Triệu chứng thường gặp: đau bụng, khó tiêu, buồn nôn"},
                {"content": "Cần có chế độ ăn uống hợp lý và vệ sinh thực phẩm"},
                {"content": "Tập thể dục đều đặn giúp hệ tiêu hóa hoạt động tốt"},
                {"content": "Khám sức khỏe định kỳ để phát hiện sớm bệnh lý"}
            ]
        },
        "slide_summaries": [
            {
                "slide_number": 1,
                "placeholders": ["TitleName", "SubtitleContent"],
                "placeholder_counts": {"TitleName": 1, "SubtitleContent": 1}
            },
            {
                "slide_number": 2,
                "placeholders": ["TitleName", "SubtitleContent"],
                "placeholder_counts": {"TitleName": 1, "SubtitleContent": 2}
            },
            {
                "slide_number": 3,
                "placeholders": ["TitleName", "SubtitleContent"],
                "placeholder_counts": {"TitleName": 1, "SubtitleContent": 1}
            },
            {
                "slide_number": 4,
                "placeholders": ["TitleName", "SubtitleContent"],
                "placeholder_counts": {"TitleName": 1, "SubtitleContent": 1}
            },
            {
                "slide_number": 5,
                "placeholders": ["TitleName", "SubtitleContent"],
                "placeholder_counts": {"TitleName": 1, "SubtitleContent": 2}
            },
            {
                "slide_number": 6,
                "placeholders": ["TitleName", "SubtitleContent"],
                "placeholder_counts": {"TitleName": 1, "SubtitleContent": 1}
            }
        ]
    }
    
    print(f"📋 Test Setup:")
    print(f"   Template slides: {len(analyzed_template['slides'])}")
    print(f"   Content slides needed: {len(parsed_content['slide_summaries'])}")
    print(f"   TitleName content: {len(parsed_content['parsed_data']['TitleName'])}")
    print(f"   SubtitleContent content: {len(parsed_content['parsed_data']['SubtitleContent'])}")
    print(f"   Expected: 6 slides in exact order 1->2->3->4->5->6")
    
    try:
        # Test new mapping flow
        print(f"\n🔧 Testing new copy-based mapping flow...")
        
        result = asyncio.run(slide_service._map_parsed_content_to_slides(
            parsed_content,
            analyzed_template
        ))
        
        if result["success"]:
            slides = result["slides"]
            original_template_ids = result.get("original_template_slide_ids", [])
            
            print(f"✅ New mapping flow completed successfully!")
            print(f"   Created slides: {len(slides)}")
            print(f"   Original template IDs: {original_template_ids}")
            
            # Analyze results
            print(f"\n📊 Results Analysis:")
            
            # Check slide order
            slide_orders = [s.get('slide_order', 999) for s in slides]
            is_ordered = slide_orders == sorted(slide_orders) and slide_orders == list(range(1, len(slides) + 1))
            print(f"   Slides in correct order: {'✅' if is_ordered else '❌'}")
            print(f"   Slide orders: {slide_orders}")
            
            # Check template usage
            template_usage = {}
            for slide in slides:
                template_source = slide.get('template_source', 'Unknown')
                template_usage[template_source] = template_usage.get(template_source, 0) + 1
            
            print(f"   Template usage: {template_usage}")
            
            # Detailed slide analysis
            print(f"\n📋 Detailed Slide Analysis:")
            for i, slide in enumerate(slides):
                slide_id = slide.get('slideId')
                slide_order = slide.get('slide_order', 'N/A')
                template_source = slide.get('template_source', 'N/A')
                action = slide.get('action', 'N/A')
                elements_count = len(slide.get('elements', []))
                is_fallback = slide.get('is_fallback', False)
                
                status = "FALLBACK" if is_fallback else "EXACT_MATCH"
                
                print(f"   {i+1}. {slide_id}")
                print(f"      Order: {slide_order}, Action: {action}")
                print(f"      Template source: {template_source}")
                print(f"      Status: {status}")
                print(f"      Elements: {elements_count}")
                
                # Show content preview
                updates = slide.get('updates', {})
                if updates:
                    print(f"      Content preview:")
                    for obj_id, content in list(updates.items())[:2]:  # Show first 2
                        preview = content[:50] + '...' if len(content) > 50 else content
                        print(f"        - {obj_id}: {preview}")
            
            # Verify content consumption
            print(f"\n📈 Content Consumption Analysis:")
            total_title_used = sum(1 for s in slides for e in s.get('elements', []) if e.get('Type') == 'TitleName')
            total_subtitle_used = sum(1 for s in slides for e in s.get('elements', []) if e.get('Type') == 'SubtitleContent')
            
            print(f"   TitleName used: {total_title_used}/{len(parsed_content['parsed_data']['TitleName'])}")
            print(f"   SubtitleContent used: {total_subtitle_used}/{len(parsed_content['parsed_data']['SubtitleContent'])}")
            
            # Check if all content is consumed
            all_content_used = (
                total_title_used == len(parsed_content['parsed_data']['TitleName']) and
                total_subtitle_used <= len(parsed_content['parsed_data']['SubtitleContent'])
            )
            print(f"   All required content used: {'✅' if all_content_used else '❌'}")
            
        else:
            print(f"❌ New mapping flow failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Test exception:")

def test_exact_template_matching():
    """Test exact template matching logic"""
    
    print(f"\n🎯 Testing Exact Template Matching")
    print("=" * 40)
    
    slide_service = SlideGenerationService()
    
    # Mock template slides
    template_slides = [
        {
            "slideId": "template_1",
            "elements": [
                {"Type": "TitleName"},
                {"Type": "SubtitleContent"}
            ]
        },
        {
            "slideId": "template_2",
            "elements": [
                {"Type": "TitleName"},
                {"Type": "SubtitleContent"},
                {"Type": "SubtitleContent"}
            ]
        }
    ]
    
    # Test cases
    test_cases = [
        {
            "name": "Exact match for template_1",
            "required_placeholders": ["TitleName", "SubtitleContent"],
            "required_counts": {"TitleName": 1, "SubtitleContent": 1},
            "expected_match": "template_1"
        },
        {
            "name": "Exact match for template_2", 
            "required_placeholders": ["TitleName", "SubtitleContent"],
            "required_counts": {"TitleName": 1, "SubtitleContent": 2},
            "expected_match": "template_2"
        },
        {
            "name": "No exact match - different count",
            "required_placeholders": ["TitleName", "SubtitleContent"],
            "required_counts": {"TitleName": 2, "SubtitleContent": 1},
            "expected_match": None
        },
        {
            "name": "No exact match - different type",
            "required_placeholders": ["TitleName", "ImageContent"],
            "required_counts": {"TitleName": 1, "ImageContent": 1},
            "expected_match": None
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 Test: {test_case['name']}")
        
        result = slide_service._find_exact_matching_template(
            test_case["required_placeholders"],
            test_case["required_counts"],
            template_slides
        )
        
        matched_id = result.get("slideId") if result else None
        expected = test_case["expected_match"]
        
        if matched_id == expected:
            print(f"   ✅ PASS: Found {matched_id}")
        else:
            print(f"   ❌ FAIL: Expected {expected}, got {matched_id}")

if __name__ == "__main__":
    print("🧪 Starting New Flow Tests")
    print("=" * 50)
    
    test_exact_template_matching()
    test_new_mapping_flow()
    
    print("\n✅ All new flow tests completed!")
